/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cquang%5C%5CDesktop%5C%5Chackathon%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext-themes%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cquang%5C%5CDesktop%5C%5Chackathon%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cquang%5C%5CDesktop%5C%5Chackathon%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cquang%5C%5CDesktop%5C%5Chackathon%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cquang%5C%5CDesktop%5C%5Chackathon%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Caccessibility%5C%5Caccessibility-provider.tsx%22%2C%22ids%22%3A%5B%22AccessibilityProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cquang%5C%5CDesktop%5C%5Chackathon%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cerror-boundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&server=false!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cquang%5C%5CDesktop%5C%5Chackathon%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext-themes%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cquang%5C%5CDesktop%5C%5Chackathon%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cquang%5C%5CDesktop%5C%5Chackathon%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cquang%5C%5CDesktop%5C%5Chackathon%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cquang%5C%5CDesktop%5C%5Chackathon%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Caccessibility%5C%5Caccessibility-provider.tsx%22%2C%22ids%22%3A%5B%22AccessibilityProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cquang%5C%5CDesktop%5C%5Chackathon%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cerror-boundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&server=false! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-themes/dist/index.mjs */ \"(app-pages-browser)/./node_modules/next-themes/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/globals.css */ \"(app-pages-browser)/./src/app/globals.css\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/accessibility/accessibility-provider.tsx */ \"(app-pages-browser)/./src/components/accessibility/accessibility-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/error-boundary.tsx */ \"(app-pages-browser)/./src/components/ui/error-boundary.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cquang%5C%5CDesktop%5C%5Chackathon%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext-themes%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cquang%5C%5CDesktop%5C%5Chackathon%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cquang%5C%5CDesktop%5C%5Chackathon%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cquang%5C%5CDesktop%5C%5Chackathon%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cquang%5C%5CDesktop%5C%5Chackathon%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Caccessibility%5C%5Caccessibility-provider.tsx%22%2C%22ids%22%3A%5B%22AccessibilityProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cquang%5C%5CDesktop%5C%5Chackathon%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cerror-boundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e7337e6a1459\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHF1YW5nXFxEZXNrdG9wXFxoYWNrYXRob25cXGZyb250ZW5kXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJlNzMzN2U2YTE0NTlcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/accessibility/accessibility-provider.tsx":
/*!*****************************************************************!*\
  !*** ./src/components/accessibility/accessibility-provider.tsx ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AccessibilityProvider: () => (/* binding */ AccessibilityProvider),\n/* harmony export */   useAccessibility: () => (/* binding */ useAccessibility)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Accessibility_Contrast_Eye_EyeOff_Minus_Plus_Settings_Type_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Accessibility,Contrast,Eye,EyeOff,Minus,Plus,Settings,Type,Volume2,VolumeX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/accessibility.js\");\n/* harmony import */ var _barrel_optimize_names_Accessibility_Contrast_Eye_EyeOff_Minus_Plus_Settings_Type_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Accessibility,Contrast,Eye,EyeOff,Minus,Plus,Settings,Type,Volume2,VolumeX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Accessibility_Contrast_Eye_EyeOff_Minus_Plus_Settings_Type_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Accessibility,Contrast,Eye,EyeOff,Minus,Plus,Settings,Type,Volume2,VolumeX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/contrast.js\");\n/* harmony import */ var _barrel_optimize_names_Accessibility_Contrast_Eye_EyeOff_Minus_Plus_Settings_Type_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Accessibility,Contrast,Eye,EyeOff,Minus,Plus,Settings,Type,Volume2,VolumeX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Accessibility_Contrast_Eye_EyeOff_Minus_Plus_Settings_Type_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Accessibility,Contrast,Eye,EyeOff,Minus,Plus,Settings,Type,Volume2,VolumeX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Accessibility_Contrast_Eye_EyeOff_Minus_Plus_Settings_Type_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Accessibility,Contrast,Eye,EyeOff,Minus,Plus,Settings,Type,Volume2,VolumeX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Accessibility_Contrast_Eye_EyeOff_Minus_Plus_Settings_Type_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Accessibility,Contrast,Eye,EyeOff,Minus,Plus,Settings,Type,Volume2,VolumeX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_Accessibility_Contrast_Eye_EyeOff_Minus_Plus_Settings_Type_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Accessibility,Contrast,Eye,EyeOff,Minus,Plus,Settings,Type,Volume2,VolumeX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Accessibility_Contrast_Eye_EyeOff_Minus_Plus_Settings_Type_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Accessibility,Contrast,Eye,EyeOff,Minus,Plus,Settings,Type,Volume2,VolumeX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Accessibility_Contrast_Eye_EyeOff_Minus_Plus_Settings_Type_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Accessibility,Contrast,Eye,EyeOff,Minus,Plus,Settings,Type,Volume2,VolumeX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Accessibility_Contrast_Eye_EyeOff_Minus_Plus_Settings_Type_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Accessibility,Contrast,Eye,EyeOff,Minus,Plus,Settings,Type,Volume2,VolumeX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-x.js\");\n/**\n * Accessibility Provider\n * Provides comprehensive accessibility features and WCAG compliance\n */ /* __next_internal_client_entry_do_not_use__ useAccessibility,AccessibilityProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nconst defaultSettings = {\n    highContrast: false,\n    largeText: false,\n    reducedMotion: false,\n    screenReader: false,\n    keyboardNavigation: true,\n    fontSize: 16,\n    colorBlindMode: 'none',\n    soundEnabled: true,\n    focusIndicator: true\n};\nconst AccessibilityContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAccessibility() {\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AccessibilityContext);\n    if (!context) {\n        throw new Error('useAccessibility must be used within AccessibilityProvider');\n    }\n    return context;\n}\n_s(useAccessibility, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction AccessibilityProvider(param) {\n    let { children } = param;\n    _s1();\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultSettings);\n    const [showPanel, setShowPanel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load settings from localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AccessibilityProvider.useEffect\": ()=>{\n            const savedSettings = localStorage.getItem('accessibility-settings');\n            if (savedSettings) {\n                try {\n                    const parsed = JSON.parse(savedSettings);\n                    setSettings({\n                        ...defaultSettings,\n                        ...parsed\n                    });\n                } catch (error) {\n                    console.error('Failed to parse accessibility settings:', error);\n                }\n            }\n        }\n    }[\"AccessibilityProvider.useEffect\"], []);\n    // Save settings to localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AccessibilityProvider.useEffect\": ()=>{\n            localStorage.setItem('accessibility-settings', JSON.stringify(settings));\n            applyAccessibilitySettings(settings);\n        }\n    }[\"AccessibilityProvider.useEffect\"], [\n        settings\n    ]);\n    // Apply accessibility settings to DOM\n    const applyAccessibilitySettings = (settings)=>{\n        const root = document.documentElement;\n        // High contrast mode\n        if (settings.highContrast) {\n            root.classList.add('high-contrast');\n        } else {\n            root.classList.remove('high-contrast');\n        }\n        // Large text\n        if (settings.largeText) {\n            root.classList.add('large-text');\n        } else {\n            root.classList.remove('large-text');\n        }\n        // Reduced motion\n        if (settings.reducedMotion) {\n            root.classList.add('reduce-motion');\n        } else {\n            root.classList.remove('reduce-motion');\n        }\n        // Font size\n        root.style.setProperty('--base-font-size', \"\".concat(settings.fontSize, \"px\"));\n        // Color blind mode\n        root.setAttribute('data-colorblind-mode', settings.colorBlindMode);\n        // Focus indicator\n        if (settings.focusIndicator) {\n            root.classList.add('enhanced-focus');\n        } else {\n            root.classList.remove('enhanced-focus');\n        }\n        // Keyboard navigation\n        if (settings.keyboardNavigation) {\n            root.classList.add('keyboard-navigation');\n        } else {\n            root.classList.remove('keyboard-navigation');\n        }\n    };\n    const updateSetting = (key, value)=>{\n        setSettings((prev)=>({\n                ...prev,\n                [key]: value\n            }));\n        // Announce changes to screen readers\n        announceToScreenReader(\"\".concat(key, \" \").concat(value ? 'enabled' : 'disabled'));\n    };\n    const resetSettings = ()=>{\n        setSettings(defaultSettings);\n        announceToScreenReader('Accessibility settings reset to default');\n    };\n    const announceToScreenReader = (message)=>{\n        if (!settings.screenReader) return;\n        const announcement = document.createElement('div');\n        announcement.setAttribute('aria-live', 'polite');\n        announcement.setAttribute('aria-atomic', 'true');\n        announcement.className = 'sr-only';\n        announcement.textContent = message;\n        document.body.appendChild(announcement);\n        setTimeout(()=>{\n            document.body.removeChild(announcement);\n        }, 1000);\n    };\n    // Keyboard shortcuts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AccessibilityProvider.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"AccessibilityProvider.useEffect.handleKeyDown\": (event)=>{\n                    // Alt + A: Toggle accessibility panel\n                    if (event.altKey && event.key === 'a') {\n                        event.preventDefault();\n                        setShowPanel({\n                            \"AccessibilityProvider.useEffect.handleKeyDown\": (prev)=>!prev\n                        }[\"AccessibilityProvider.useEffect.handleKeyDown\"]);\n                        announceToScreenReader('Accessibility panel toggled');\n                    }\n                    // Alt + H: Toggle high contrast\n                    if (event.altKey && event.key === 'h') {\n                        event.preventDefault();\n                        updateSetting('highContrast', !settings.highContrast);\n                    }\n                    // Alt + T: Toggle large text\n                    if (event.altKey && event.key === 't') {\n                        event.preventDefault();\n                        updateSetting('largeText', !settings.largeText);\n                    }\n                    // Alt + M: Toggle reduced motion\n                    if (event.altKey && event.key === 'm') {\n                        event.preventDefault();\n                        updateSetting('reducedMotion', !settings.reducedMotion);\n                    }\n                }\n            }[\"AccessibilityProvider.useEffect.handleKeyDown\"];\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"AccessibilityProvider.useEffect\": ()=>document.removeEventListener('keydown', handleKeyDown)\n            })[\"AccessibilityProvider.useEffect\"];\n        }\n    }[\"AccessibilityProvider.useEffect\"], [\n        settings\n    ]);\n    const contextValue = {\n        settings,\n        updateSetting,\n        resetSettings,\n        announceToScreenReader\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AccessibilityContext.Provider, {\n        value: contextValue,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                className: \"fixed bottom-4 right-4 z-50 rounded-full w-12 h-12 p-0 shadow-lg\",\n                onClick: ()=>setShowPanel(!showPanel),\n                \"aria-label\": \"Open accessibility settings\",\n                title: \"Accessibility Settings (Alt + A)\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accessibility_Contrast_Eye_EyeOff_Minus_Plus_Settings_Type_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-5 w-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.AnimatePresence, {\n                children: showPanel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        scale: 0.9,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        scale: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        scale: 0.9,\n                        y: 20\n                    },\n                    className: \"fixed bottom-20 right-4 z-50 w-80\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"shadow-xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-lg flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accessibility_Contrast_Eye_EyeOff_Minus_Plus_Settings_Type_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Accessibility\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"icon\",\n                                            onClick: ()=>setShowPanel(false),\n                                            \"aria-label\": \"Close accessibility panel\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accessibility_Contrast_Eye_EyeOff_Minus_Plus_Settings_Type_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accessibility_Contrast_Eye_EyeOff_Minus_Plus_Settings_Type_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"High Contrast\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: settings.highContrast ? \"default\" : \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>updateSetting('highContrast', !settings.highContrast),\n                                                \"aria-pressed\": settings.highContrast,\n                                                children: settings.highContrast ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accessibility_Contrast_Eye_EyeOff_Minus_Plus_Settings_Type_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 46\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accessibility_Contrast_Eye_EyeOff_Minus_Plus_Settings_Type_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 76\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accessibility_Contrast_Eye_EyeOff_Minus_Plus_Settings_Type_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Large Text\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: settings.largeText ? \"default\" : \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>updateSetting('largeText', !settings.largeText),\n                                                \"aria-pressed\": settings.largeText,\n                                                children: settings.largeText ? 'On' : 'Off'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Font Size\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: [\n                                                            settings.fontSize,\n                                                            \"px\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"icon\",\n                                                        onClick: ()=>updateSetting('fontSize', Math.max(12, settings.fontSize - 2)),\n                                                        disabled: settings.fontSize <= 12,\n                                                        \"aria-label\": \"Decrease font size\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accessibility_Contrast_Eye_EyeOff_Minus_Plus_Settings_Type_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 bg-gray-200 h-2 rounded\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-blue-500 h-2 rounded transition-all\",\n                                                            style: {\n                                                                width: \"\".concat((settings.fontSize - 12) / (24 - 12) * 100, \"%\")\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"icon\",\n                                                        onClick: ()=>updateSetting('fontSize', Math.min(24, settings.fontSize + 2)),\n                                                        disabled: settings.fontSize >= 24,\n                                                        \"aria-label\": \"Increase font size\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accessibility_Contrast_Eye_EyeOff_Minus_Plus_Settings_Type_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accessibility_Contrast_Eye_EyeOff_Minus_Plus_Settings_Type_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Reduced Motion\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: settings.reducedMotion ? \"default\" : \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>updateSetting('reducedMotion', !settings.reducedMotion),\n                                                \"aria-pressed\": settings.reducedMotion,\n                                                children: settings.reducedMotion ? 'On' : 'Off'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    settings.soundEnabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accessibility_Contrast_Eye_EyeOff_Minus_Plus_Settings_Type_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 46\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accessibility_Contrast_Eye_EyeOff_Minus_Plus_Settings_Type_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 80\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Sound Effects\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: settings.soundEnabled ? \"default\" : \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>updateSetting('soundEnabled', !settings.soundEnabled),\n                                                \"aria-pressed\": settings.soundEnabled,\n                                                children: settings.soundEnabled ? 'On' : 'Off'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"Color Blind Support\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: settings.colorBlindMode,\n                                                onChange: (e)=>updateSetting('colorBlindMode', e.target.value),\n                                                className: \"w-full p-2 border rounded text-sm\",\n                                                \"aria-label\": \"Color blind mode\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"none\",\n                                                        children: \"None\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"protanopia\",\n                                                        children: \"Protanopia\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"deuteranopia\",\n                                                        children: \"Deuteranopia\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"tritanopia\",\n                                                        children: \"Tritanopia\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        onClick: resetSettings,\n                                        className: \"w-full\",\n                                        children: \"Reset to Default\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Keyboard Shortcuts:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 22\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Alt + A: Toggle this panel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Alt + H: Toggle high contrast\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Alt + T: Toggle large text\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Alt + M: Toggle reduced motion\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                \"aria-live\": \"polite\",\n                \"aria-atomic\": \"true\",\n                className: \"sr-only\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n                lineNumber: 394,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\accessibility\\\\accessibility-provider.tsx\",\n        lineNumber: 215,\n        columnNumber: 5\n    }, this);\n}\n_s1(AccessibilityProvider, \"221ZjRDfXhnA1WVVH8KqYZE1qdE=\");\n_c = AccessibilityProvider;\nvar _c;\n$RefreshReg$(_c, \"AccessibilityProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/accessibility/accessibility-provider.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/error-boundary.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/error-boundary.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiError: () => (/* binding */ ApiError),\n/* harmony export */   EmptyState: () => (/* binding */ EmptyState),\n/* harmony export */   ErrorBoundary: () => (/* binding */ ErrorBoundary),\n/* harmony export */   NetworkError: () => (/* binding */ NetworkError)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Home,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Home,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Home,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Home,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/**\n * Professional Error Boundary Components\n * Provides graceful error handling and recovery\n */ /* __next_internal_client_entry_do_not_use__ ErrorBoundary,ApiError,NetworkError,EmptyState auto */ \n\n\n\n\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        this.setState({\n            error,\n            errorInfo\n        });\n        // Log error to monitoring service\n        console.error('Error Boundary caught an error:', error, errorInfo);\n        // Call custom error handler if provided\n        if (this.props.onError) {\n            this.props.onError(error, errorInfo);\n        }\n    }\n    render() {\n        if (this.state.hasError) {\n            if (this.props.fallback) {\n                return this.props.fallback;\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorFallback, {\n                error: this.state.error,\n                onRetry: this.handleRetry,\n                onReload: this.handleReload,\n                onGoHome: this.handleGoHome\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                lineNumber: 80,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    }\n    constructor(props){\n        super(props), this.handleRetry = ()=>{\n            this.setState({\n                hasError: false,\n                error: null,\n                errorInfo: null\n            });\n        }, this.handleReload = ()=>{\n            window.location.reload();\n        }, this.handleGoHome = ()=>{\n            window.location.href = '/';\n        };\n        this.state = {\n            hasError: false,\n            error: null,\n            errorInfo: null\n        };\n    }\n}\nfunction ErrorFallback(param) {\n    let { error, onRetry, onReload, onGoHome } = param;\n    const isDevelopment = \"development\" === 'development';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-orange-50 p-4\",\n        initial: {\n            opacity: 0,\n            scale: 0.9\n        },\n        animate: {\n            opacity: 1,\n            scale: 1\n        },\n        transition: {\n            duration: 0.3\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full bg-white rounded-xl shadow-xl p-8 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    className: \"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n                    initial: {\n                        scale: 0\n                    },\n                    animate: {\n                        scale: 1\n                    },\n                    transition: {\n                        delay: 0.1,\n                        type: 'spring',\n                        stiffness: 200\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"w-8 h-8 text-red-500\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.h1, {\n                    className: \"text-2xl font-bold text-gray-900 mb-2\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: 0.2\n                    },\n                    children: \"Oops! Something went wrong\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.p, {\n                    className: \"text-gray-600 mb-6\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: 0.3\n                    },\n                    children: \"We encountered an unexpected error. Don't worry, our team has been notified.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, this),\n                isDevelopment && error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.details, {\n                    className: \"text-left mb-6 p-4 bg-gray-50 rounded-lg\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: 0.4\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                            className: \"cursor-pointer text-sm font-medium text-gray-700 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"inline w-4 h-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 15\n                                }, this),\n                                \"Error Details (Development)\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"text-xs text-red-600 overflow-auto max-h-32\",\n                            children: [\n                                error.message,\n                                error.stack && \"\\n\\n\".concat(error.stack)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    className: \"space-y-3\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: 0.5\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: onRetry,\n                            className: \"w-full bg-blue-600 hover:bg-blue-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this),\n                                \"Try Again\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: onReload,\n                                    variant: \"outline\",\n                                    className: \"flex-1\",\n                                    children: \"Reload Page\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: onGoHome,\n                                    variant: \"outline\",\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Go Home\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n            lineNumber: 110,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, this);\n}\n_c = ErrorFallback;\nfunction ApiError(param) {\n    let { error, onRetry, className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        className: \"bg-red-50 border border-red-200 rounded-lg p-4 \".concat(className),\n        initial: {\n            opacity: 0,\n            scale: 0.95\n        },\n        animate: {\n            opacity: 1,\n            scale: 1\n        },\n        transition: {\n            duration: 0.2\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium text-red-800 mb-1\",\n                            children: \"API Error\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-red-700 mb-3\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this),\n                        onRetry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: onRetry,\n                            size: \"sm\",\n                            variant: \"outline\",\n                            className: \"border-red-300 text-red-700 hover:bg-red-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-3 h-3 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 15\n                                }, this),\n                                \"Retry\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n            lineNumber: 208,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n        lineNumber: 202,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ApiError;\n// Network Error Component\nfunction NetworkError(param) {\n    let { onRetry } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        className: \"bg-orange-50 border border-orange-200 rounded-lg p-4\",\n        initial: {\n            opacity: 0,\n            scale: 0.95\n        },\n        animate: {\n            opacity: 1,\n            scale: 1\n        },\n        transition: {\n            duration: 0.2\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-5 h-5 text-orange-500 mr-3\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium text-orange-800 mb-1\",\n                            children: \"Connection Error\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-orange-700 mb-3\",\n                            children: \"Unable to connect to the server. Please check your internet connection.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, this),\n                        onRetry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: onRetry,\n                            size: \"sm\",\n                            variant: \"outline\",\n                            className: \"border-orange-300 text-orange-700 hover:bg-orange-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-3 h-3 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 15\n                                }, this),\n                                \"Retry Connection\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n            lineNumber: 243,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n        lineNumber: 237,\n        columnNumber: 5\n    }, this);\n}\n_c2 = NetworkError;\nfunction EmptyState(param) {\n    let { title, description, action, icon } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        className: \"text-center py-12\",\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.3\n        },\n        children: [\n            icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                className: \"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                initial: {\n                    scale: 0\n                },\n                animate: {\n                    scale: 1\n                },\n                transition: {\n                    delay: 0.1,\n                    type: 'spring',\n                    stiffness: 200\n                },\n                children: icon\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                lineNumber: 289,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                lineNumber: 299,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-600 mb-6 max-w-sm mx-auto\",\n                children: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                lineNumber: 300,\n                columnNumber: 7\n            }, this),\n            action && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                onClick: action.onClick,\n                children: action.label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                lineNumber: 303,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n        lineNumber: 282,\n        columnNumber: 5\n    }, this);\n}\n_c3 = EmptyState;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ErrorFallback\");\n$RefreshReg$(_c1, \"ApiError\");\n$RefreshReg$(_c2, \"NetworkError\");\n$RefreshReg$(_c3, \"EmptyState\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/error-boundary.tsx\n"));

/***/ })

});