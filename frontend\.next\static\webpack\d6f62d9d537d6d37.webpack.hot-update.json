{"c": ["app/layout", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cquang%5C%5CDesktop%5C%5Chackathon%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext-themes%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cquang%5C%5CDesktop%5C%5Chackathon%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cquang%5C%5CDesktop%5C%5Chackathon%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cquang%5C%5CDesktop%5C%5Chackathon%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cquang%5C%5CDesktop%5C%5Chackathon%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Caccessibility%5C%5Caccessibility-provider.tsx%22%2C%22ids%22%3A%5B%22AccessibilityProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cquang%5C%5CDesktop%5C%5Chackathon%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Csession-provider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cquang%5C%5CDesktop%5C%5Chackathon%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cerror-boundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cquang%5C%5CDesktop%5C%5Chackathon%5C%5Cfrontend%5C%5Csrc%5C%5Clib%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=false!", "(app-pages-browser)/./src/components/providers/session-provider.tsx", "(app-pages-browser)/./src/lib/auth-context.tsx", "(app-pages-browser)/./node_modules/next-auth/lib/client.js", "(app-pages-browser)/./node_modules/next-auth/node_modules/@auth/core/errors.js", "(app-pages-browser)/./node_modules/next-auth/react.js"]}