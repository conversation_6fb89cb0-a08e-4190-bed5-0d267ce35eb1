{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "H5T7swY/5kYvtyW373yhXKB4bgs0ZlRsdfRIC1rMaDI=", "__NEXT_PREVIEW_MODE_ID": "c2b47d2d46976aeec9eca2bda8622ad9", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "81cb5389279ba0637023c369957cc7e7595d330c3fa8de4a932035afddc6b033", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2bd6aba721ce45a8034efe0a651631b3eee12b6c16929c99ed15721fa8d1ead5"}}}, "functions": {}, "sortedMiddleware": ["/"]}