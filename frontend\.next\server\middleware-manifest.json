{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "H5T7swY/5kYvtyW373yhXKB4bgs0ZlRsdfRIC1rMaDI=", "__NEXT_PREVIEW_MODE_ID": "c8186f7b01bc0c2e132c9389b3963bf5", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d3f28e2d7c107462742c05a327e8e048474ccef486e950a68748e493bee3ffc0", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "5605deb7a712d745073673107d233d3d63918104500e3fb28d75b6274ba69138"}}}, "functions": {}, "sortedMiddleware": ["/"]}